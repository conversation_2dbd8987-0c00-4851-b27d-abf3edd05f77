{"train_micro_batch_size_per_gpu": 2, "gradient_accumulation_steps": 4, "steps_per_print": 50, "optimizer": {"type": "AdamW", "params": {"lr": 5e-05, "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.01}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": 0, "warmup_max_lr": 5e-05, "warmup_num_steps": 500}}, "zero_optimization": {"stage": 3, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "offload_param": {"device": "cpu", "pin_memory": true}, "overlap_comm": true, "contiguous_gradients": true, "sub_group_size": 1000000000.0, "reduce_bucket_size": 1000000.0, "stage3_prefetch_bucket_size": 1000000.0, "stage3_param_persistence_threshold": 10000.0, "stage3_max_live_parameters": 1000000000.0, "stage3_max_reuse_distance": 1000000000.0, "stage3_gather_16bit_weights_on_model_save": true}, "fp16": {"enabled": true, "loss_scale": 0, "loss_scale_window": 1000, "initial_scale_power": 16, "hysteresis": 2, "min_loss_scale": 1}, "gradient_clipping": 1.0, "wall_clock_breakdown": false, "memory_breakdown": false, "comms_logger": {"enabled": false}}