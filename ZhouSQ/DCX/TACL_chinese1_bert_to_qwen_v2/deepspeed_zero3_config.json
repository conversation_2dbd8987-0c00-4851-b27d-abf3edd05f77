{"train_micro_batch_size_per_gpu": 4, "gradient_accumulation_steps": 8, "steps_per_print": 100, "optimizer": {"type": "AdamW", "params": {"lr": 3e-05, "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.01}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": 0, "warmup_max_lr": 3e-05, "warmup_num_steps": 1000}}, "zero_optimization": {"stage": 3, "offload_optimizer": {"device": "none"}, "offload_param": {"device": "none"}, "overlap_comm": true, "contiguous_gradients": true, "reduce_bucket_size": 500000000.0, "stage3_prefetch_bucket_size": 90000000.0, "stage3_param_persistence_threshold": 1000000.0}, "bf16": {"enabled": true}, "gradient_clipping": 1.0, "wall_clock_breakdown": false}