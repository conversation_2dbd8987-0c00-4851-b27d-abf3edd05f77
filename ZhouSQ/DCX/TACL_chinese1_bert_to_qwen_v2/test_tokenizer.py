#!/usr/bin/env python3
"""
测试tokenizer和模型的兼容性
"""
import torch
import sys
import os
sys.path.append('/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL')

from util.utils import load_plm_from_config
import argparse

def test_tokenizer_model_compatibility():
    """测试tokenizer和模型的兼容性"""
    
    # 创建简单的args对象
    class Args:
        def __init__(self):
            self.dropout = 0.1
            self.device = 0
            self.use_deepspeed = 0
    
    args = Args()
    model_path = '/home/<USER>/ZhouSQ/DCX/Qwen3-8B'
    
    print("🔍 开始测试tokenizer和模型兼容性...")
    
    try:
        # 加载模型和tokenizer
        plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, model_path)
        print(f"✅ 成功加载模型和tokenizer")
        print(f"📊 Tokenizer词汇表大小: {len(tokenizer)}")
        print(f"📊 模型embedding大小: {plm.get_input_embeddings().weight.shape[0]}")
        
        # 测试基本tokenization
        test_text = "这是一个测试文本。"
        tokens = tokenizer(test_text, return_tensors='pt')
        print(f"📝 测试文本: {test_text}")
        print(f"📝 Token IDs: {tokens['input_ids']}")
        print(f"📝 Token IDs范围: {tokens['input_ids'].min().item()} - {tokens['input_ids'].max().item()}")
        
        # 检查token ID是否在有效范围内
        max_token_id = tokens['input_ids'].max().item()
        vocab_size = plm.get_input_embeddings().weight.shape[0]
        
        if max_token_id >= vocab_size:
            print(f"❌ 错误: Token ID {max_token_id} 超出了模型词汇表大小 {vocab_size}")
            return False
        else:
            print(f"✅ 所有Token ID都在有效范围内")
        
        # 测试模型前向传播
        plm.eval()
        with torch.no_grad():
            if torch.cuda.is_available():
                plm = plm.cuda()
                tokens = {k: v.cuda() for k, v in tokens.items()}
            
            outputs = plm(**tokens)
            print(f"✅ 模型前向传播成功")
            print(f"📊 输出形状: {outputs.logits.shape}")
        
        # 测试特殊token
        special_tokens = ['mask_token', 'pad_token', 'eos_token', 'bos_token', 'unk_token']
        for token_name in special_tokens:
            if hasattr(tokenizer, token_name):
                token = getattr(tokenizer, token_name)
                token_id = getattr(tokenizer, f"{token_name}_id", None)
                if token is not None and token_id is not None:
                    print(f"📝 {token_name}: '{token}' (ID: {token_id})")
                    if token_id >= vocab_size:
                        print(f"❌ 警告: {token_name} ID {token_id} 超出词汇表大小")
        
        print("🎉 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tokenizer_model_compatibility()
    if not success:
        sys.exit(1)
