'''
Author: jike
Date: 2022-10-08 09:40:03
LastEditTime: 2022-11-21 15:57:29
LastEditors: jike
FilePath: /mnt/jike/paper/nlu/paper/train.py
'''

from datetime import datetime
import logging
from tqdm import tqdm
import os
os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
# 设置PyTorch内存优化环境变量
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
import torch
# 启用内存优化
torch.backends.cuda.matmul.allow_tf32 = True
torch.backends.cudnn.allow_tf32 = True
import argparse
import openprompt
from openprompt.utils.reproduciblity import set_seed
from openprompt.prompts import SoftVerbalizer, ManualTemplate

from models.hierVerb import HierVerbPromptForClassification

from processor import PROCESSOR
from processor_des import PROCESSOR1

from util.utils import load_plm_from_config, print_info
from util.data_loader import SinglePathPromptDataLoader
from transformers.optimization import get_linear_schedule_with_warmup
from torch.optim import AdamW
try:
    import bitsandbytes as bnb
    BITSANDBYTES_AVAILABLE = True
except ImportError:
    BITSANDBYTES_AVAILABLE = False
    print("Warning: bitsandbytes not available, using standard AdamW")

try:
    import deepspeed
    DEEPSPEED_AVAILABLE = True
except ImportError:
    DEEPSPEED_AVAILABLE = False
    print("Warning: deepspeed not available")

try:
    from peft import LoraConfig, get_peft_model, TaskType
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False
    print("Warning: peft not available, LoRA will not work")
from torch.utils.tensorboard import SummaryWriter

import logging

logging.basicConfig(format='%(asctime)s - %(levelname)s - %(name)s -   %(message)s',
                    datefmt='%m/%d/%Y %H:%M:%S',
                    level=logging.INFO)
logger = logging.getLogger(__name__)

use_cuda = True


def main():
    start_time = datetime.now()
    parser = argparse.ArgumentParser("")

    parser.add_argument("--model", type=str, default='qwen')
    parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/Qwen3-8B')
    parser.add_argument("--result_file", type=str, default="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/few_shot_train.txt")

    parser.add_argument("--multi_label", default=0, type=int) # 是否多标签分类
    parser.add_argument("--multi_verb", default=1, type=int) # 是否多动词分类

    parser.add_argument("--constraint_loss", default=1, type=int) # 约束损失
    parser.add_argument("--constraint_alpha", default=0.1, type=float) # 约束损失的权重

    parser.add_argument("--contrastive_loss", default=1, type=int) # 对比损失
    parser.add_argument("--contrastive_alpha", default=0.2, type=float) # 对比损失的权重
    parser.add_argument("--contrastive_level", default=2, type=int) # 对比损失的层数

    parser.add_argument("--lm_training", default=1, type=int) # 是否进行语言模型训练
    parser.add_argument("--lm_alpha", default=0.7, type=float) # 提高语言模型损失权重，帮助模型学习

    parser.add_argument("--lr", default=3e-5, type=float) # 提高PLM学习率，确保有效微调
    parser.add_argument("--lr2", default=5e-4, type=float) # verbalizer使用适中的学习率

    parser.add_argument("--batch_size", default=8, type=int) # 使用适中的batch size，避免显存问题
    parser.add_argument("--depth", default=7, type=int) # 学科层数

    parser.add_argument("--multi_mask", type=int, default=1)

    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=1, type=int)
    parser.add_argument("--contrastive_logits", default=1, type=int)
    parser.add_argument("--cs_mode", default=0, type=int)
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)  # 默认不冻结，允许微调

    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)

    parser.add_argument("--imbalanced_weight", default=True, type=bool)
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)

    parser.add_argument("--device", default=-1, type=int)  # 使用-1来启用多GPU模式

    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="Max gradient norm.")
    parser.add_argument("--max_seq_lens", default=512, type=int, help="Max sequence length.")

    parser.add_argument("--use_new_ct", default=1, type=int)
    parser.add_argument("--use_dropout_sim", default=1, type=int)
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)

    parser.add_argument('--mean_verbalizer', default=True, type=bool)
    parser.add_argument("--shot", type=int, default=30) 
    parser.add_argument("--label_description", type=int, default=0)
    parser.add_argument("--seed", type=int, default=171)
    parser.add_argument("--plm_eval_mode", default=True)
    parser.add_argument("--verbalizer", type=str, default="soft")
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--not_manual", default=False, type=int)

    parser.add_argument("--gradient_accumulation_steps", type=int, default=4)  # 减少梯度累积，加快收敛
    parser.add_argument("--max_epochs", type=int, default=20)  # 增加epoch数量，允许充分训练
    
    parser.add_argument("--early_stop", default=5, type=int)
    parser.add_argument("--eval_full", default=0, type=int)
    parser.add_argument("--use_deepspeed", default=1, type=int, help="使用DeepSpeed ZeRO")
    # parser.add_argument("--deepspeed_config", default="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/deepspeed_zero3_config.json", type=str)

    # LoRA参数 - 如果显存不足，可以启用LoRA
    parser.add_argument("--use_lora", default=0, type=int, help="使用LoRA微调")
    parser.add_argument("--lora_rank", default=32, type=int, help="LoRA rank - 增加rank提高表达能力")
    parser.add_argument("--lora_alpha", default=64, type=int, help="LoRA alpha - 增加alpha提高学习率")
    parser.add_argument("--lora_dropout", default=0.05, type=float, help="LoRA dropout - 降低dropout")
    parser.add_argument("--lora_target_modules", default="q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj", type=str, help="LoRA目标模块")

    # DeepSpeed参数
    parser.add_argument("--local_rank", type=int, default=-1, help="DeepSpeed local rank")

    args = parser.parse_args()

    # DeepSpeed分布式训练初始化
    if args.use_deepspeed and DEEPSPEED_AVAILABLE:
        import deepspeed
        deepspeed.init_distributed()

        # DeepSpeed模式下使用local_rank
        if args.local_rank != -1:
            torch.cuda.set_device(args.local_rank)
            device = torch.device(f"cuda:{args.local_rank}")
        else:
            device = torch.device("cuda:0")
        use_cuda = True
        print_info(f"Using DeepSpeed distributed training, local_rank: {args.local_rank}")

    elif args.device == -1:
        # 多GPU模式：不设置CUDA_VISIBLE_DEVICES，让device_map="auto"自动分配
        device = torch.device("cuda:0")
        use_cuda = True
        print_info("Using multi-GPU mode with device_map='auto'")
    elif args.device >= 0:
        # 单GPU模式：指定特定的GPU
        os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"
        device = torch.device("cuda:0")
        use_cuda = True
        print_info(f"Using single GPU: {args.device}")
    else:
        # CPU模式
        use_cuda = False
        device = torch.device("cpu")
        print_info("Using CPU mode")

    if args.contrastive_loss == 0:
        args.contrastive_logits = 0
        args.use_dropout_sim = 0

    if args.shuffle == 1:
        args.shuffle = True
    else:
        args.shuffle = False
    print_info(args)
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)

    if args.label_description:
        processor1 = PROCESSOR1[args.dataset](shot=args.shot, seed=args.seed)
        train_data = processor1.train_example
        dev_data = processor1.dev_example
        test_data = processor1.test_example
        # dataset
        dataset = {}
        dataset['train'] = processor1.train_example
        dataset['dev'] = processor1.dev_example
        dataset['test'] = processor1.test_example
    else:
        train_data = processor.train_example
        dev_data = processor.dev_example
        test_data = processor.test_example
        # dataset
        dataset = {}
        dataset['train'] = processor.train_example
        dataset['dev'] = processor.dev_example
        dataset['test'] = processor.test_example
    train_data = [[i.text_a, i.label] for i in train_data]
    dev_data = [[i.text_a, i.label] for i in dev_data]
    test_data = [[i.text_a, i.label] for i in test_data]
    hier_mapping = processor.hier_mapping
    args.depth = len(hier_mapping) + 1

    print_info("final train_data length is: {}".format(len(train_data)))
    print_info("final dev_data length is: {}".format(len(dev_data)))
    print_info("final test_data length is: {}".format(len(test_data)))

    args.template_id = 0

    set_seed(args.seed)

    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)

    # 应用LoRA配置
    if args.use_lora and PEFT_AVAILABLE:
        print_info("🔧 应用LoRA配置...")

        # 配置LoRA参数
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=args.lora_rank,
            lora_alpha=args.lora_alpha,
            lora_dropout=args.lora_dropout,
            target_modules=args.lora_target_modules.split(","),
            bias="none",
            inference_mode=False,
        )

        # 应用LoRA到模型
        plm = get_peft_model(plm, lora_config)

        # 确保embedding层保持正确的数据类型
        if hasattr(plm, 'base_model') and hasattr(plm.base_model, 'model'):
            if hasattr(plm.base_model.model, 'embed_tokens'):
                plm.base_model.model.embed_tokens.weight.data = plm.base_model.model.embed_tokens.weight.data.to(torch.float16)

        # 打印可训练参数信息
        trainable_params = sum(p.numel() for p in plm.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in plm.parameters())
        print_info(f"✅ LoRA配置完成!")
        print_info(f"📊 可训练参数: {trainable_params:,} ({100 * trainable_params / total_params:.2f}%)")
        print_info(f"📊 总参数: {total_params:,}")

        # 设置freeze_plm为0，因为我们要训练LoRA参数
        args.freeze_plm = 0
    elif args.use_lora and not PEFT_AVAILABLE:
        print_info("❌ PEFT库不可用，无法使用LoRA，将使用标准微调")
        args.use_lora = 0

    max_seq_l = args.max_seq_lens
    batch_s = args.batch_size

    if args.multi_mask:
        template_file = f"{args.dataset}_mask_template.txt"
    else:
        template_file = "manual_template.txt"
    # 使用绝对路径，将所有输出放在DCX目录下
    base_dir = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/"
    template_dir = os.path.join(base_dir, "template")
    ckpts_dir = os.path.join(base_dir, "ckpts")
    runs_dir = os.path.join(base_dir, "runs")
    result_dir = os.path.join(base_dir, "result")

    # 创建必要的目录
    for dir_path in [template_dir, ckpts_dir, runs_dir, result_dir]:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

    text_mask = []
    for i in range(args.depth):
        text_mask.append(f'{i + 1} level: {{"mask"}}')
    text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
    template_path = os.path.join(template_dir, template_file)
    if not os.path.exists(template_path):
        with open(template_path, 'w', encoding='utf-8') as fp:
            fp.write(text)
    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)

    print_info("train_size: {}".format(len(dataset['train'])))

    ## Loading dataset
    train_dataloader = SinglePathPromptDataLoader(dataset=dataset['train'], template=mytemplate, tokenizer=tokenizer,
                                                  tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                  decoder_max_length=3,
                                                  batch_size=batch_s, shuffle=args.shuffle, teacher_forcing=False,
                                                  predict_eos_token=False, truncate_method="tail",
                                                  num_works=2,
                                                  multi_gpu=(args.device == -2), )
    if args.dataset == "wos":
        full_name = "WebOfScience"
    elif args.dataset == "dbp":
        full_name = "DBpedia"
    else:
        raise NotImplementedError
    

    test_path = os.path.join(f"/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL/dataset", full_name, f"test_dataloader-multi_mask.pt")
    dev_path = os.path.join(f"/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL/dataset", full_name, f"dev_dataloader-multi_mask.pt")
    # eval_batch_s = 20
    eval_batch_s = 3
    if args.dataset != "dbp" and os.path.exists(dev_path):
        validation_dataloader = torch.load(dev_path, weights_only=False)

    else:
        validation_dataloader = SinglePathPromptDataLoader(dataset=dataset["dev"], template=mytemplate,
                                                           tokenizer=tokenizer,
                                                           tokenizer_wrapper_class=WrapperClass,
                                                           max_seq_length=max_seq_l,
                                                           decoder_max_length=3,
                                                           batch_size=eval_batch_s, shuffle=False,
                                                           teacher_forcing=False,
                                                           predict_eos_token=False,
                                                           truncate_method="tail",
                                                           multi_gpu=False,
                                                           )
        if args.dataset != "dbp":
            torch.save(validation_dataloader, dev_path)
    if not os.path.exists(test_path):
        test_dataloader = SinglePathPromptDataLoader(dataset=dataset["test"], template=mytemplate, tokenizer=tokenizer,
                                                     tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                     decoder_max_length=3,
                                                     batch_size=eval_batch_s, shuffle=False, teacher_forcing=False,
                                                     predict_eos_token=False,
                                                     truncate_method="tail",
                                                     multi_gpu=False,
                                                     mode='test',
                                                     )
        torch.save(test_dataloader, test_path)
    else:
        test_dataloader = torch.load(test_path, weights_only=False)

    ## build verbalizer and model
    verbalizer_list = []
    label_list = processor.label_list

    for i in range(args.depth):
        if "0.1.2" in openprompt.__path__[0]:
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))
        else:
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))

    print_info("loading prompt model")
    prompt_model = HierVerbPromptForClassification(plm=plm, template=mytemplate, verbalizer_list=verbalizer_list,tokenizer=tokenizer,
                                              freeze_plm=args.freeze_plm, args=args, processor=processor,
                                              plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda)

    # DeepSpeed模式下不需要手动移动到CUDA
    if use_cuda and args.device != -1 and not (args.use_deepspeed and DEEPSPEED_AVAILABLE):
        # 只在单GPU模式下手动移动到CUDA，多GPU模式下已经通过device_map自动分配
        prompt_model = prompt_model.cuda()

    ## Prepare training parameters
    # it's always good practice to set no decay to biase and LayerNorm parameters
    no_decay = ['bias', 'LayerNorm.weight']

    if args.use_lora and PEFT_AVAILABLE:
        # LoRA模式：只优化可训练的参数（LoRA参数）
        print_info("🔧 配置LoRA优化器...")
        trainable_params = [(n, p) for n, p in prompt_model.plm.named_parameters() if p.requires_grad]
        print_info(f"📊 LoRA可训练参数数量: {len(trainable_params)}")

        optimizer_grouped_parameters1 = [
            {'params': [p for n, p in trainable_params if not any(nd in n for nd in no_decay)],
             'weight_decay': 0.01},
            {'params': [p for n, p in trainable_params if any(nd in n for nd in no_decay)],
             'weight_decay': 0.0}
        ]
    else:
        # 标准模式：优化所有PLM参数
        named_parameters = prompt_model.plm.named_parameters()
        optimizer_grouped_parameters1 = [
            {'params': [p for n, p in named_parameters if not any(nd in n for nd in no_decay)],
             'weight_decay': 0.01},
            {'params': [p for n, p in named_parameters if any(nd in n for nd in no_decay)],
             'weight_decay': 0.0}
        ]

    # Using different optimizer for prompt parameters and model parameters
    # use a learning rate of 1e−4 to fasten the convergence of its hierarchical label words’ embeddings of verbalizer0
    verbalizer = prompt_model.verbalizer
    optimizer_grouped_parameters2 = [
        {'params': verbalizer.group_parameters_1, "lr": args.lr},
        {'params': verbalizer.group_parameters_2, "lr": args.lr2},
    ]

    # DeepSpeed模式下的优化器配置
    if args.use_deepspeed and DEEPSPEED_AVAILABLE:
        print_info("🚀 使用DeepSpeed ZeRO Stage 3")

        # 打印参数数量，确认是否有需要训练的参数
        total = sum(p.numel() for p in prompt_model.parameters())
        trainable = sum(p.numel() for p in prompt_model.parameters() if p.requires_grad)
        print_info(f"Total params: {total:,}, Trainable params: {trainable:,}")

        # 确保模型在正确的设备上
        if use_cuda:
            prompt_model = prompt_model.cuda()

        # 初始化DeepSpeed - 当使用deepspeed命令启动时，不需要传递config参数
        # DeepSpeed会自动从命令行参数中读取配置
        prompt_model, optimizer1, _, scheduler1 = deepspeed.initialize(
            args=args,
            model=prompt_model,
            model_parameters=optimizer_grouped_parameters1
        )

        # verbalizer优化器仍然使用标准方式
        optimizer2 = AdamW(optimizer_grouped_parameters2)

        print_info("✅ DeepSpeed初始化完成")

    else:
        # 标准模式的优化器配置
        if BITSANDBYTES_AVAILABLE:
            print("Using 8bit AdamW optimizer to save memory")
            optimizer1 = bnb.optim.AdamW8bit(optimizer_grouped_parameters1, lr=args.lr)
            optimizer2 = bnb.optim.AdamW8bit(optimizer_grouped_parameters2)
        else:
            print("Using standard AdamW optimizer")
            optimizer1 = AdamW(optimizer_grouped_parameters1, lr=args.lr)
            optimizer2 = AdamW(optimizer_grouped_parameters2)

    tot_step = len(train_dataloader) // args.gradient_accumulation_steps * args.max_epochs

    # 添加warmup步数，帮助稳定训练
    warmup_steps = int(0.05 * tot_step)  # 5%的步数用于warmup，避免过长的warmup
    print_info(f"Total steps: {tot_step}, Warmup steps: {warmup_steps}")
    # 学习率调度器配置
    if not (args.use_deepspeed and DEEPSPEED_AVAILABLE):
        # 非DeepSpeed模式下的调度器
        scheduler1 = None
        scheduler2 = None
        if args.use_scheduler1:
            scheduler1 = get_linear_schedule_with_warmup(
                optimizer1,
                num_warmup_steps=warmup_steps, num_training_steps=tot_step)
        if args.use_scheduler2:
            scheduler2 = get_linear_schedule_with_warmup(
                optimizer2,
                num_warmup_steps=warmup_steps, num_training_steps=tot_step)
    else:
        # DeepSpeed模式下，scheduler1已经在initialize中创建
        scheduler2 = None
        if args.use_scheduler2:
            scheduler2 = get_linear_schedule_with_warmup(
                optimizer2,
                num_warmup_steps=warmup_steps, num_training_steps=tot_step)

    contrastive_alpha = args.contrastive_alpha
    best_score_macro = 0
    best_score_micro = 0
    best_score_macro_epoch = -1
    best_score_micro_epoch = -1
    early_stop_count = 0

    if not args.imbalanced_weight:
        args.imbalanced_weight_reverse = False

    # 获取当前时间，并格式化
    current_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')

    # 构建新的字符串
    this_run_unicode = f"{current_time}-lr-{args.lr}-lm_training-{args.lm_training}-lm_alpha-{args.lm_alpha}-batch_size-{args.batch_size}"

    print_info("saved_path: {}".format(this_run_unicode))

    # === TensorBoard SummaryWriter 初始化 ===
    writer = SummaryWriter(log_dir=os.path.join(runs_dir, "train", this_run_unicode))
    global_step = 0

    # === 写入超参数到 TensorBoard hparams ===
    hparams = {k: v for k, v in vars(args).items()}
    writer.add_text('hparams', str(hparams))

    if args.eval_full:
        best_record = dict()
        keys = ['p_micro_f1', 'p_macro_f1', 'c_micro_f1', 'c_macro_f1', 'P_acc']
        for key in keys:
            best_record[key] = 0

    ## start training
    for epoch in range(args.max_epochs):
        print_info("------------ epoch {} ------------".format(epoch + 1))
        if early_stop_count >= args.early_stop:
            print_info("Early stop!")
            break

        # === 记录当前学习率到 TensorBoard ===
        if scheduler1 is not None:
            writer.add_scalar('LR/scheduler1', scheduler1.get_last_lr()[0], epoch)
        else:
            writer.add_scalar('LR/scheduler1', args.lr, epoch)
        if scheduler2 is not None:
            writer.add_scalar('LR/scheduler2', scheduler2.get_last_lr()[0], epoch)
        else:
            writer.add_scalar('LR/scheduler2', args.lr2, epoch)

        print_info(
            f"cur lr\tscheduler1: {scheduler1.get_lr() if scheduler1 is not None else args.lr}\tscheduler2: {scheduler2.get_lr() if scheduler2 is not None else 1e-4}")

        loss_detailed = [0, 0, 0, 0]
        prompt_model.train()
        idx = 0

        for batch in tqdm(train_dataloader):
            # 简化数据处理，因为我们已经在模型层面修复了embedding的数据类型问题
            batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
            batch = {"input_ids": batch[0], "attention_mask": batch[1],
                     "label": batch[2], "loss_ids": batch[3]}

            logits, loss, cur_loss_detailed = prompt_model(batch)

            # === 检查loss是否为NaN ===
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"Warning: NaN or Inf loss detected at batch {idx}, skipping...")
                continue

            # === 添加模型输出的打印 ===
            if idx % 50 == 0:  # 每50个batch打印一次，更频繁监控
                print(f"\n=== Batch {idx} 模型输出 ===")
                
                # 打印输入文本
                input_text = tokenizer.decode(batch['input_ids'][0], skip_special_tokens=True)
                print(f"输入文本: {input_text}")
                
                # 打印预测的标签名称
                if isinstance(logits, list):
                    for depth, logit in enumerate(logits):
                        pred_idx = torch.argmax(logit[0], dim=-1).item()
                        pred_label = processor.label_list[depth][pred_idx]
                        print(f"第{depth}层预测: {pred_label} (索引: {pred_idx})")
                
                # 打印真实标签名称
                true_label_idx = batch['label'][0].item()
                # 获取真实标签的完整路径
                true_path = []
                current_idx = true_label_idx
                for depth in range(len(processor.label_list)-1, -1, -1):
                    if depth == len(processor.label_list)-1:
                        true_path.insert(0, processor.label_list[depth][current_idx])
                    else:
                        # 根据层级映射获取上层标签
                        parent_idx = processor.hier_mapping[depth][1][current_idx]
                        true_path.insert(0, processor.label_list[depth][parent_idx])
                        current_idx = parent_idx
                
                print(f"真实标签路径: {' -> '.join(true_path)}")
                print(f"总Loss: {loss.item():.4f}")
                if isinstance(cur_loss_detailed, (list, tuple)) and len(cur_loss_detailed) >= 2:
                    print(f"分类Loss: {cur_loss_detailed[0]:.4f}, LM Loss: {cur_loss_detailed[1]:.4f}")
                print("=" * 60)
            
            loss_detailed = [loss_detailed[idx] + value for idx, value in enumerate(cur_loss_detailed)]

            if args.use_deepspeed and DEEPSPEED_AVAILABLE:
                # DeepSpeed模式下的反向传播和优化
                prompt_model.backward(loss)
                prompt_model.step()

                # verbalizer优化器仍然手动处理
                optimizer2.step()
                if scheduler2 is not None:
                    scheduler2.step()
                optimizer2.zero_grad()

            else:
                # 标准模式下的反向传播和优化
                loss.backward()
                torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), args.max_grad_norm)

                optimizer1.step()
                optimizer2.step()

                if scheduler1 is not None:
                    scheduler1.step()
                if scheduler2 is not None:
                    scheduler2.step()

                optimizer1.zero_grad()
                optimizer2.zero_grad()

            # === 写入TensorBoard loss ===
            # writer.add_scalar('Loss/train', loss.item(), global_step)
            if isinstance(cur_loss_detailed, (list, tuple)) and len(cur_loss_detailed) == 4:
                writer.add_scalar('Loss/multi-verb', cur_loss_detailed[0], global_step)
                writer.add_scalar('Loss/lm', cur_loss_detailed[1], global_step)
                writer.add_scalar('Loss/constraint', cur_loss_detailed[2], global_step)
                writer.add_scalar('Loss/contrastive', cur_loss_detailed[3], global_step)
            global_step += 1

            idx = idx + 1
            # 每10个batch清理一次显存
            if idx % 10 == 0:
                torch.cuda.empty_cache()
        print_info("multi-verb loss, lm loss, constraint loss, contrastive loss are: ")
        print_info(loss_detailed)

        # === 训练集评估并写入TensorBoard ===
        train_scores = prompt_model.evaluate(train_dataloader, processor, desc="Train", mode=args.eval_mode)
        if 'macro_f1' in train_scores:
            writer.add_scalar('Train/macro_f1', train_scores['macro_f1'], epoch)
        if 'micro_f1' in train_scores:
            writer.add_scalar('Train/micro_f1', train_scores['micro_f1'], epoch)
        if 'acc' in train_scores:
            writer.add_scalar('Train/acc', train_scores['acc'], epoch)

        scores = prompt_model.evaluate(validation_dataloader, processor, desc="Valid",
                                       mode=args.eval_mode)
        # === 写入TensorBoard 验证集指标 ===
        if 'macro_f1' in scores:
            writer.add_scalar('Val/macro_f1', scores['macro_f1'], epoch)
        if 'micro_f1' in scores:
            writer.add_scalar('Val/micro_f1', scores['micro_f1'], epoch)
        if 'acc' in scores:
            writer.add_scalar('Val/acc', scores['acc'], epoch)

        early_stop_count += 1
        if args.eval_full:
            score_str = ""
            for key in keys:
                score_str += f'{key} {scores[key]}\n'
            print_info(score_str)
            for k in best_record:
                if scores[k] > best_record[k]:
                    best_record[k] = scores[k]
                    torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-{k}.ckpt")
                    early_stop_count = 0

        else:
            macro_f1 = scores['macro_f1']
            micro_f1 = scores['micro_f1']
            print_info('macro {} micro {}'.format(macro_f1, micro_f1))
            if macro_f1 > best_score_macro:
                best_score_macro = macro_f1
                # LoRA模式下保存LoRA权重
                if args.use_lora and PEFT_AVAILABLE:
                    lora_save_path = os.path.join(ckpts_dir, f"{this_run_unicode}-macro-lora")
                    prompt_model.plm.save_pretrained(lora_save_path)
                    print_info(f"💾 保存LoRA权重到: {lora_save_path}")
                else:
                    ckpt_save_path = os.path.join(ckpts_dir, f"{this_run_unicode}-macro.ckpt")
                    if args.use_deepspeed and DEEPSPEED_AVAILABLE:
                        # DeepSpeed模式下保存模型
                        prompt_model.save_checkpoint(ckpts_dir, f"{this_run_unicode}-macro")
                        print_info(f"💾 DeepSpeed保存模型到: {ckpts_dir}/{this_run_unicode}-macro")
                    else:
                        torch.save(prompt_model.state_dict(), ckpt_save_path)
                early_stop_count = 0
                best_score_macro_epoch = epoch

            if micro_f1 > best_score_micro:
                best_score_micro = micro_f1
                # LoRA模式下保存LoRA权重
                if args.use_lora and PEFT_AVAILABLE:
                    lora_save_path = os.path.join(ckpts_dir, f"{this_run_unicode}-micro-lora")
                    prompt_model.plm.save_pretrained(lora_save_path)
                    print_info(f"💾 保存LoRA权重到: {lora_save_path}")
                else:
                    ckpt_save_path = os.path.join(ckpts_dir, f"{this_run_unicode}-micro.ckpt")
                    if args.use_deepspeed and DEEPSPEED_AVAILABLE:
                        # DeepSpeed模式下保存模型
                        prompt_model.save_checkpoint(ckpts_dir, f"{this_run_unicode}-micro")
                        print_info(f"💾 DeepSpeed保存模型到: {ckpts_dir}/{this_run_unicode}-micro")
                    else:
                        torch.save(prompt_model.state_dict(), ckpt_save_path)
                early_stop_count = 0
                best_score_micro_epoch = epoch

    ## evaluate
    if args.eval_full:
        best_keys = ['P_acc']
        for k in best_keys:
            prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-{k}.ckpt"))

            scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode,
                                           args=args)
            
            tmp_str = ''
            tmp_str += f"finally best_{k} "
            for i in keys:
                tmp_str += f"{i}: {scores[i]}\t"
                writer.add_scalar(f"Test/{i}", scores[i])
            print_info(tmp_str)

    else:
        # for best macro
        if args.use_deepspeed and DEEPSPEED_AVAILABLE:
            # DeepSpeed模式下加载模型
            ckpt_load_path = os.path.join(ckpts_dir, f"{this_run_unicode}-macro")
            prompt_model.load_checkpoint(ckpt_load_path)
            print_info(f"🔄 DeepSpeed加载模型从: {ckpt_load_path}")
        else:
            ckpt_load_path = os.path.join(ckpts_dir, f"{this_run_unicode}-macro.ckpt")
            prompt_model.load_state_dict(torch.load(ckpt_load_path))

            if use_cuda and args.device != -1:
                # 只在单GPU模式下手动移动到CUDA，多GPU模式下已经通过device_map自动分配
                prompt_model = prompt_model.cuda()

        scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode)
        macro_f1_1 = scores['macro_f1']
        micro_f1_1 = scores['micro_f1']
        acc_1 = scores['acc']
        print_info('finally best macro {} {} micro {} acc {}'.format(best_score_macro_epoch, macro_f1_1, micro_f1_1, acc_1))
        writer.add_scalar('Test/macro_f1', macro_f1_1)
        writer.add_scalar('Test/micro_f1', micro_f1_1)
        writer.add_scalar('Test/acc', acc_1)

        # for best micro
        ckpt_load_path = os.path.join(ckpts_dir, f"{this_run_unicode}-micro.ckpt")
        prompt_model.load_state_dict(torch.load(ckpt_load_path))

        scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode)
        macro_f1_2 = scores['macro_f1']
        micro_f1_2 = scores['micro_f1']
        acc_2 = scores['acc']
        print_info('finally best micro {} {} micro {} acc {}'.format(best_score_micro_epoch, macro_f1_2, micro_f1_2, acc_2))
        writer.add_scalar('Test/macro_f1_micro', macro_f1_2)
        writer.add_scalar('Test/micro_f1_micro', micro_f1_2)
        writer.add_scalar('Test/acc_micro', acc_2)

    ## print and record parameter details
    content_write = "=" * 20 + "\n"
    content_write += f"start_time {start_time}" + "\n"
    content_write += f"end_time {datetime.now()}\t"
    for hyperparam, value in args.__dict__.items():
        content_write += f"{hyperparam} {value}\t"
    content_write += "\n"
    if args.eval_full:
        cur_keys = ['P_acc']
        for key in cur_keys:
            content_write += f"best_{key} "
            for i in keys:
                content_write += f"{i}: {best_record[i]}\t"
            content_write += f"\n"
    else:
        content_write += f"best_macro macro_f1: {macro_f1_1}\t"
        content_write += f"micro_f1: {micro_f1_1}\t"
        content_write += f"acc: {acc_1}\t\n"

        content_write += f"best_micro macro_f1: {macro_f1_2}\t"
        content_write += f"micro_f1: {micro_f1_2}\t"
        content_write += f"acc: {acc_2}\t"
    content_write += "\n\n"

    print_info(content_write)
    result_file_path = os.path.join(result_dir, os.path.basename(args.result_file))
    with open(result_file_path, "a") as fout:
        fout.write(content_write)

    # === 关闭TensorBoard writer ===
    writer.close()


if __name__ == "__main__":
    main()
