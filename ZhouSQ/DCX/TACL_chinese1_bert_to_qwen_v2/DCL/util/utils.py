# -*- coding:utf-8 -*-
import torch
import numpy as np
from transformers import __version__ as transformers_version
import random
from transformers import <PERSON><PERSON>oken<PERSON>, AutoTokenizer

from transformers import <PERSON><PERSON>onfig, BertForMaskedLM, AutoConfig, AutoModelForCausalLM
from openprompt.plms.mlm import MLMTokenizerWrapper
from openprompt.plms.lm import LMTokenizerWrapper
import argparse

logger = None


def print_info(info, file=None):
    if logger is not None:
        logger.info(info)
    else:
        print(info, file=file)


def parse_args(model="hierCRF"):
    parser = argparse.ArgumentParser("")

    parser.add_argument("--model", type=str, default=model, choices=['hierVerb', 'hierCRF'])
    parser.add_argument("--model_name_or_path", default='/root/autodl-tmp/htc/bert-base-uncased')
    parser.add_argument("--result_file", type=str, default="few_shot_train.txt")
    parser.add_argument("--multi_mask", type=int, default=1)
    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=0, type=int)

    parser.add_argument("--do_train", default=1, type=int)
    parser.add_argument("--do_dev", default=1, type=bool)
    parser.add_argument("--do_test", default=1, type=bool)

    parser.add_argument("--not_manual", default=False, type=int)
    parser.add_argument("--depth", default=2, type=int)

    parser.add_argument("--device", default=0, type=int)

    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)

    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)

    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)

    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="Max gradient norm.")
    parser.add_argument("--max_seq_lens", default=512, type=int, help="Max sequence length.")
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)
    parser.add_argument('--mean_verbalizer', default=True, type=bool)

    parser.add_argument("--shot", type=int, default=1)
    parser.add_argument("--seed", type=int, default=171)

    parser.add_argument("--freeze_plm", default=0, type=int)

    parser.add_argument("--plm_eval_mode", default=False)
    parser.add_argument("--verbalizer", type=str, default="soft")

    parser.add_argument("--template_id", default=0, type=int)

    parser.add_argument("--multi_label", default=0, type=int)

    parser.add_argument("--early_stop", default=10, type=int)
    parser.add_argument("--eval_full", default=0, type=int)
    if model == "hierVerb":
        parser.add_argument("--use_new_ct", default=1, type=int)
        parser.add_argument("--contrastive_loss", default=1, type=int)
        parser.add_argument("--contrastive_level", default=1, type=int)
        parser.add_argument("--contrastive_alpha", default=0.99, type=float)
        parser.add_argument("--contrastive_logits", default=1, type=int)
        parser.add_argument("--use_dropout_sim", default=1, type=int)
        parser.add_argument("--imbalanced_weight", default=True, type=bool)
        parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)
        parser.add_argument("--max_epochs", type=int, default=20)
        parser.add_argument("--constraint_loss", default=1, type=int)
        parser.add_argument("--constraint_alpha", default=-1, type=float)
        parser.add_argument("--cs_mode", default=0, type=int)

        parser.add_argument("--lm_training", default=1, type=int)
        parser.add_argument("--lm_alpha", default=0.999, type=float)

        parser.add_argument("--lr", default=5e-5, type=float)
        parser.add_argument("--lr2", default=1e-4, type=float)

        parser.add_argument("--batch_size", default=5, type=int)
        parser.add_argument("--eval_batch_size", default=20, type=int)

        args = parser.parse_args()
        return args
    elif model == "hierCRF":

        parser.add_argument("--lr", default=5e-5, type=float)
        parser.add_argument("--lr2", default=1e-4, type=float)
        parser.add_argument("--lr3", default=5e-2, type=float)
        parser.add_argument("--max_epochs", type=int, default=50)
        parser.add_argument("--hierCRF_loss", default=1, type=int)

        parser.add_argument("--hierCRF_alpha", default=-1, type=float)
        parser.add_argument("--batch_size", default=10, type=int)
        parser.add_argument("--eval_batch_size", default=20, type=int)

        parser.add_argument("--multi_verb_loss", default=1, type=int)
        parser.add_argument("--multi_verb_loss_alpha", default=-1, type=int)

        parser.add_argument("--lm_training", default=0, type=int)
        parser.add_argument("--lm_alpha", default=0.999, type=float)

        args = parser.parse_args()
        return args
    else:
        raise NotImplementedError


def load_plm_from_config(args, model_path, specials_to_add=None, **kwargs):
    r"""A plm loader using a global config.
    It will load the model, tokenizer, and config simulatenously.
    Support both BERT and Qwen models.

    Args:
        config (:obj:`CfgNode`): The global config from the CfgNode.

    Returns:
        :obj:`PreTrainedModel`: The pretrained model.
        :obj:`tokenizer`: The pretrained tokenizer.
        :obj:`model_config`: The config of the pretrained model.
        :obj:`wrapper`: The wrapper class of this plm.
    """
    # 检查是否是Qwen模型
    if 'qwen' in model_path.lower() or 'Qwen' in model_path:
        return load_qwen_from_config(args, model_path, specials_to_add, **kwargs)
    else:
        # 原有的BERT加载逻辑
        model_config = BertConfig.from_pretrained(model_path)
        model_config.hidden_dropout_prob = args.dropout
        model = BertForMaskedLM.from_pretrained(model_path, config=model_config)
        tokenizer = BertTokenizer.from_pretrained(model_path)
        wrapper = MLMTokenizerWrapper
        return model, tokenizer, model_config, wrapper


def load_qwen_from_config(args, model_path, specials_to_add=None, **kwargs):
    r"""A Qwen model loader.

    Args:
        args: Arguments containing model configuration
        model_path: Path to the Qwen model

    Returns:
        :obj:`PreTrainedModel`: The pretrained Qwen model.
        :obj:`tokenizer`: The pretrained tokenizer.
        :obj:`model_config`: The config of the pretrained model.
        :obj:`wrapper`: The wrapper class of this plm.
    """
    # 加载Qwen配置和模型
    model_config = AutoConfig.from_pretrained(model_path)

    # 设置dropout（如果配置中有对应参数）
    if hasattr(model_config, 'hidden_dropout_prob'):
        model_config.hidden_dropout_prob = args.dropout
    elif hasattr(model_config, 'attention_dropout'):
        model_config.attention_dropout = args.dropout

    # 检查是否使用多GPU或DeepSpeed
    use_multi_gpu = (hasattr(args, 'device') and args.device == -1) or (hasattr(args, 'use_deepspeed') and args.use_deepspeed)

    # 加载因果语言模型而不是MLM模型
    if use_multi_gpu:
        # 检查是否使用DeepSpeed
        use_deepspeed = hasattr(args, 'use_deepspeed') and args.use_deepspeed

        if use_deepspeed:
            # DeepSpeed模式：不使用device_map，让DeepSpeed处理分布式
            model = None  # 初始化model变量
            try:
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    config=model_config,
                    torch_dtype=torch.float16,  # 使用float16避免数据类型问题
                    low_cpu_mem_usage=True  # 减少CPU内存使用
                )
                print("✅ DeepSpeed模式：使用float16加载模型")
            except Exception as e:
                print(f"DeepSpeed float16失败，使用float32: {e}")
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    config=model_config,
                    torch_dtype=torch.float32,  # 使用float32作为fallback
                    low_cpu_mem_usage=True
                )
                print("⚠️ DeepSpeed模式：使用float32加载模型")

            # DeepSpeed模式下也添加embedding层保护
            if model is not None and hasattr(model, 'model') and hasattr(model.model, 'embed_tokens'):
                original_forward = model.model.embed_tokens.forward

                def safe_embedding_forward(input_ids):
                    if input_ids.dtype != torch.long:
                        input_ids = input_ids.long()
                    # 额外保护：确保所有token ID都在有效范围内
                    vocab_size = original_forward.__self__.weight.shape[0]
                    input_ids = torch.clamp(input_ids, 0, vocab_size - 1)
                    return original_forward(input_ids)

                model.model.embed_tokens.forward = safe_embedding_forward
        else:
            # 标准多GPU模式：使用device_map="auto"来自动分配多GPU
            model = None  # 初始化model变量
            try:
                # 尝试使用Flash Attention V3 + float16 (避免bfloat16的兼容性问题)
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    config=model_config,
                    torch_dtype=torch.float16,  # 使用float16避免数据类型问题
                    device_map="auto",  # 自动分配到多个GPU
                    low_cpu_mem_usage=True,  # 减少CPU内存使用
                    attn_implementation="flash_attention_2"  # 使用最新的flash attention
                )
                print("✅ 使用Flash Attention V3 + float16加载模型")
            except Exception as e:
                print(f"Flash Attention失败，尝试标准attention: {e}")
                try:
                    # 如果Flash Attention失败，使用标准attention + float16
                    model = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        config=model_config,
                        torch_dtype=torch.float16,  # 使用float16避免数据类型问题
                        device_map="auto",  # 自动分配到多个GPU
                        low_cpu_mem_usage=True  # 减少CPU内存使用
                    )
                    print("✅ 使用标准attention + float16加载模型")
                except Exception as e2:
                    print(f"float16失败，使用float16: {e2}")
                    # 最后的fallback：使用float16
                    model = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        config=model_config,
                        torch_dtype=torch.float16,  # 使用float16以节省显存
                        device_map="auto",  # 自动分配到多个GPU
                        low_cpu_mem_usage=True  # 减少CPU内存使用
                    )
                    print("⚠️ 使用float16加载模型")

            # 启用梯度检查点以节省显存
            if model is not None:
                model.gradient_checkpointing_enable()
                # 禁用KV缓存以节省显存
                model.config.use_cache = False

                # 重要修复：确保embedding层保持float16，但不影响输入数据类型检查
                # 这样可以避免自动类型转换导致的问题
                if hasattr(model, 'model') and hasattr(model.model, 'embed_tokens'):
                    # 保存原始的forward方法
                    original_forward = model.model.embed_tokens.forward

                    def safe_embedding_forward(input_ids):
                        # 确保输入是long类型，即使模型是float16
                        if input_ids.dtype != torch.long:
                            input_ids = input_ids.long()
                        # 额外保护：确保所有token ID都在有效范围内
                        vocab_size = original_forward.__self__.weight.shape[0]
                        input_ids = torch.clamp(input_ids, 0, vocab_size - 1)
                        return original_forward(input_ids)

                    # 替换forward方法
                    model.model.embed_tokens.forward = safe_embedding_forward
    else:
        # 单GPU或CPU模式
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            config=model_config,
            torch_dtype=torch.float32,  # 使用float32以确保兼容性
            device_map=None  # 不自动分配设备
        )

        # 为单GPU模式也添加embedding层保护
        if hasattr(model, 'model') and hasattr(model.model, 'embed_tokens'):
            original_forward = model.model.embed_tokens.forward

            def safe_embedding_forward(input_ids):
                if input_ids.dtype != torch.long:
                    input_ids = input_ids.long()
                # 额外保护：确保所有token ID都在有效范围内
                vocab_size = original_forward.__self__.weight.shape[0]
                input_ids = torch.clamp(input_ids, 0, vocab_size - 1)
                return original_forward(input_ids)

            model.model.embed_tokens.forward = safe_embedding_forward

    # 加载tokenizer，尝试使用慢速tokenizer以兼容openprompt
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
    except:
        # 如果慢速tokenizer不可用，使用快速tokenizer但需要特殊处理
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print("Warning: Using fast tokenizer, may have compatibility issues with openprompt")

    # 确保tokenizer有pad_token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # 为Qwen tokenizer添加mask_token（因为它是因果语言模型，原本没有mask token）
    if not hasattr(tokenizer, 'mask_token') or tokenizer.mask_token is None:
        # 使用一个特殊的token作为mask token
        tokenizer.mask_token = '<|mask|>'
        # 将mask token添加到词汇表中
        if '<|mask|>' not in tokenizer.get_vocab():
            # 记录添加前的词汇表大小
            original_vocab_size = len(tokenizer)
            tokenizer.add_special_tokens({'mask_token': '<|mask|>'})
            new_vocab_size = len(tokenizer)

            # 如果词汇表大小发生了变化，需要调整模型的embedding层
            if new_vocab_size > original_vocab_size:
                print(f"📝 词汇表大小从 {original_vocab_size} 增加到 {new_vocab_size}")
                model.resize_token_embeddings(new_vocab_size)
                print(f"✅ 已调整模型embedding层大小到 {new_vocab_size}")

        tokenizer.mask_token_id = tokenizer.convert_tokens_to_ids('<|mask|>')

    # 为Qwen tokenizer添加兼容性方法
    if 'qwen' in model_path.lower() or 'Qwen' in model_path:
        def get_special_tokens_mask_fixed(token_ids, already_has_special_tokens=False):
            """Fixed version of get_special_tokens_mask for Qwen tokenizer"""
            if already_has_special_tokens:
                return [0] * len(token_ids)

            special_tokens = set()
            for attr in ['bos_token_id', 'eos_token_id', 'pad_token_id', 'unk_token_id']:
                if hasattr(tokenizer, attr):
                    token_id = getattr(tokenizer, attr)
                    if token_id is not None:
                        special_tokens.add(token_id)

            return [1 if token_id in special_tokens else 0 for token_id in token_ids]

        # 替换原方法
        tokenizer.get_special_tokens_mask = get_special_tokens_mask_fixed

    # 对于Qwen，我们仍然使用MLM wrapper，但需要确保tokenizer兼容性
    wrapper = MLMTokenizerWrapper

    return model, tokenizer, model_config, wrapper


def seed_torch(seed=1029):
    print('Set seed to', seed)
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True


def _mask_tokens(tokenizer, input_ids):
    """ Prepare masked tokens inputs/labels for masked language modeling: 80% MASK, 10% random, 10% original.
    For Qwen models, we adapt this to work with causal language modeling.
    """
    labels = input_ids.clone()

    # 检查是否是Qwen tokenizer（没有mask_token）
    if not hasattr(tokenizer, 'mask_token') or tokenizer.mask_token is None:
        # 对于Qwen等因果语言模型，我们使用不同的策略
        # 可以选择禁用MLM训练，或者使用其他token作为mask
        print("Warning: Tokenizer does not have mask_token. Using unk_token or pad_token as mask.")
        mask_token_id = tokenizer.unk_token_id if tokenizer.unk_token_id is not None else tokenizer.pad_token_id
        if mask_token_id is None:
            # 如果都没有，使用一个安全的token ID
            mask_token_id = min(tokenizer.vocab_size - 1, 0)  # 确保不会超出范围
    else:
        mask_token_id = tokenizer.convert_tokens_to_ids(tokenizer.mask_token)

    # We sample a few tokens in each sequence for masked-LM training (with probability 0.15)
    probability_matrix = torch.full(labels.shape, 0.15)
    special_tokens_mask = [tokenizer.get_special_tokens_mask(val, already_has_special_tokens=True) for val in
                           labels.tolist()]
    probability_matrix.masked_fill_(torch.tensor(special_tokens_mask, dtype=torch.bool), value=0.0)

    masked_indices = torch.bernoulli(probability_matrix).bool()

    # if a version of transformers < 2.4.0 is used, -1 is the expected value for indices to ignore
    if [int(v) for v in transformers_version.split('.')][:3] >= [2, 4, 0]:
        ignore_value = -100
    else:
        ignore_value = -1

    labels[~masked_indices] = ignore_value  # We only compute loss on masked tokens

    # 80% of the time, we replace masked input tokens with mask token
    indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8)).bool() & masked_indices
    input_ids[indices_replaced] = mask_token_id

    # 10% of the time, we replace masked input tokens with random word
    indices_random = torch.bernoulli(torch.full(labels.shape, 0.5)).bool() & masked_indices & ~indices_replaced
    # 确保随机token ID在有效范围内
    vocab_size = len(tokenizer)
    random_words = torch.randint(0, vocab_size, labels.shape, dtype=torch.long)
    input_ids[indices_random] = random_words[indices_random]

    # The rest of the time (10% of the time) we keep the masked input tokens unchanged
    return input_ids, labels
