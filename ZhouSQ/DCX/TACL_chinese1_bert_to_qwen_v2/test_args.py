#!/usr/bin/env python3
"""
测试参数解析是否正确
"""

import sys
import os
from pathlib import Path

# 添加DCL目录到Python路径
sys.path.append(str(Path(__file__).parent / "DCL"))

def test_args():
    """测试参数解析"""
    
    # 模拟启动脚本中的参数
    test_args = [
        "--model", "qwen",
        "--model_name_or_path", "/home/<USER>/ZhouSQ/DCX/Qwen3-8B",
        "--result_file", "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/few_shot_train.txt",
        "--multi_label", "0",
        "--multi_verb", "1",
        "--constraint_loss", "1",
        "--constraint_alpha", "0.1",
        "--lm_training", "1",
        "--lm_alpha", "0.7",
        "--lr", "3e-5",
        "--lr2", "5e-4",
        "--contrastive_loss", "1",
        "--contrastive_alpha", "0.2",
        "--contrastive_level", "2",
        "--batch_size", "8",
        "--depth", "7",
        "--multi_mask", "1",
        "--dropout", "0.1",
        "--shuffle", "True",
        "--contrastive_logits", "1",
        "--cs_mode", "0",
        "--dataset", "wos",
        "--eval_mode", "0",
        "--use_hier_mean", "1",
        "--freeze_plm", "0",
        "--use_scheduler1", "1",
        "--use_scheduler2", "1",
        "--imbalanced_weight", "True",
        "--imbalanced_weight_reverse", "True",
        "--device", "-1",
        "--max_grad_norm", "1.0",
        "--max_seq_lens", "512",
        "--use_new_ct", "1",
        "--use_dropout_sim", "1",
        "--use_withoutWrappedLM", "False",
        "--mean_verbalizer", "True",
        "--shot", "30",
        "--label_description", "0",
        "--seed", "171",
        "--plm_eval_mode", "False",
        "--verbalizer", "soft",
        "--template_id", "0",
        "--not_manual", "False",
        "--gradient_accumulation_steps", "4",
        "--max_epochs", "20",
        "--early_stop", "5",
        "--eval_full", "0",
        "--use_deepspeed", "1",
        "--deepspeed_config", "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/deepspeed_zero3_config.json",
        "--use_lora", "0",
        "--lora_rank", "32",
        "--lora_alpha", "64",
        "--lora_dropout", "0.05",
        "--lora_target_modules", "q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj"
    ]
    
    try:
        # 导入训练脚本
        import train_tb
        
        # 创建参数解析器
        parser = train_tb.argparse.ArgumentParser("")
        
        # 添加所有参数（复制train_tb.py中的参数定义）
        parser.add_argument("--model", type=str, default='bert')
        parser.add_argument("--model_name_or_path", default='bert-base-uncased')
        parser.add_argument("--result_file", type=str, default="few_shot_train.txt")
        parser.add_argument("--multi_label", type=int, default=0)
        parser.add_argument("--multi_verb", type=int, default=1)
        parser.add_argument("--constraint_loss", type=int, default=1)
        parser.add_argument("--constraint_alpha", type=float, default=0.1)
        parser.add_argument("--lm_training", type=int, default=1)
        parser.add_argument("--lm_alpha", type=float, default=0.3)
        parser.add_argument("--lr", type=float, default=5e-5)
        parser.add_argument("--lr2", type=float, default=1e-3)
        parser.add_argument("--contrastive_loss", type=int, default=0)
        parser.add_argument("--contrastive_alpha", type=float, default=0.1)
        parser.add_argument("--contrastive_level", type=int, default=2)
        parser.add_argument("--batch_size", type=int, default=8)
        parser.add_argument("--depth", type=int, default=7)
        parser.add_argument("--multi_mask", type=int, default=1)
        parser.add_argument("--dropout", type=float, default=0.05)
        parser.add_argument("--shuffle", type=bool, default=True)
        parser.add_argument("--contrastive_logits", type=int, default=0)
        parser.add_argument("--cs_mode", type=int, default=0)
        parser.add_argument("--dataset", type=str, default='wos')
        parser.add_argument("--eval_mode", type=int, default=0)
        parser.add_argument("--use_hier_mean", type=int, default=1)
        parser.add_argument("--freeze_plm", type=int, default=0)
        parser.add_argument("--use_scheduler1", type=int, default=1)
        parser.add_argument("--use_scheduler2", type=int, default=1)
        parser.add_argument("--imbalanced_weight", type=bool, default=True)
        parser.add_argument("--imbalanced_weight_reverse", type=bool, default=True)
        parser.add_argument("--device", type=int, default=-1)
        parser.add_argument("--max_grad_norm", type=float, default=1.0)
        parser.add_argument("--max_seq_lens", type=int, default=512)
        parser.add_argument("--use_new_ct", type=int, default=1)
        parser.add_argument("--use_dropout_sim", type=int, default=0)
        parser.add_argument("--use_withoutWrappedLM", type=bool, default=False)
        parser.add_argument("--mean_verbalizer", type=bool, default=True)
        parser.add_argument("--shot", type=int, default=30)
        parser.add_argument("--label_description", type=int, default=0)
        parser.add_argument("--seed", type=int, default=171)
        parser.add_argument("--plm_eval_mode", type=bool, default=False)
        parser.add_argument("--verbalizer", type=str, default='soft')
        parser.add_argument("--template_id", type=int, default=0)
        parser.add_argument("--not_manual", type=bool, default=False)
        parser.add_argument("--gradient_accumulation_steps", type=int, default=2)
        parser.add_argument("--max_epochs", type=int, default=20)
        parser.add_argument("--early_stop", type=int, default=5)
        parser.add_argument("--eval_full", type=int, default=0)
        parser.add_argument("--use_deepspeed", type=int, default=1)
        parser.add_argument("--deepspeed_config", type=str, default="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/deepspeed_zero3_config.json")
        parser.add_argument("--use_lora", type=int, default=0)
        parser.add_argument("--lora_rank", type=int, default=32)
        parser.add_argument("--lora_alpha", type=int, default=64)
        parser.add_argument("--lora_dropout", type=float, default=0.05)
        parser.add_argument("--lora_target_modules", type=str, default="q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj")
        parser.add_argument("--local_rank", type=int, default=-1)
        
        # 解析参数
        args = parser.parse_args(test_args)
        
        print("✅ 参数解析成功！")
        print(f"   - use_deepspeed: {args.use_deepspeed}")
        print(f"   - deepspeed_config: {args.deepspeed_config}")
        print(f"   - lm_alpha: {args.lm_alpha}")
        print(f"   - lr: {args.lr}")
        print(f"   - contrastive_loss: {args.contrastive_loss}")
        print(f"   - use_dropout_sim: {args.use_dropout_sim}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数解析失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试参数解析")
    print("=" * 40)
    
    if test_args():
        print("-" * 40)
        print("🎉 测试通过！现在可以运行训练了")
        print("\n📋 使用以下命令启动训练:")
        print("bash run_deepspeed.sh")
    else:
        print("-" * 40)
        print("❌ 测试失败，请检查参数配置")
