#!/usr/bin/env python3
"""
DeepSpeed集成测试脚本
用于验证DeepSpeed ZeRO-3配置是否正确
"""

import os
import sys
import json
import torch
import argparse
from pathlib import Path

# 添加DCL目录到Python路径
sys.path.append(str(Path(__file__).parent / "DCL"))

try:
    import deepspeed
    DEEPSPEED_AVAILABLE = True
    print(f"✅ DeepSpeed {deepspeed.__version__} 可用")
except ImportError:
    DEEPSPEED_AVAILABLE = False
    print("❌ DeepSpeed 不可用")
    sys.exit(1)

def test_deepspeed_config():
    """测试DeepSpeed配置文件"""
    config_path = "deepspeed_zero3_config.json"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"✅ 配置文件加载成功: {config_path}")
        print(f"   - ZeRO Stage: {config['zero_optimization']['stage']}")
        print(f"   - 微批次大小: {config['train_micro_batch_size_per_gpu']}")
        print(f"   - 梯度累积步数: {config['gradient_accumulation_steps']}")
        print(f"   - FP16: {config.get('fp16', {}).get('enabled', False)}")
        print(f"   - 优化器Offload: {config['zero_optimization']['offload_optimizer']['device']}")
        print(f"   - 参数Offload: {config['zero_optimization']['offload_param']['device']}")
        
        return True
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    try:
        from util.utils import load_plm_from_config
        from transformers import AutoTokenizer
        
        # 创建简单的参数对象
        class Args:
            model = "qwen"
            model_name_or_path = "/home/<USER>/ZhouSQ/DCX/Qwen3-8B"
            use_deepspeed = True
            device = -1
        
        args = Args()
        
        print("🔄 测试模型加载...")
        
        # 检查模型路径是否存在
        if not os.path.exists(args.model_name_or_path):
            print(f"❌ 模型路径不存在: {args.model_name_or_path}")
            return False
        
        print(f"✅ 模型路径存在: {args.model_name_or_path}")
        
        # 测试tokenizer加载
        try:
            tokenizer = AutoTokenizer.from_pretrained(args.model_name_or_path, trust_remote_code=True)
            print(f"✅ Tokenizer加载成功，词汇表大小: {len(tokenizer)}")
        except Exception as e:
            print(f"❌ Tokenizer加载失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        return False

def test_deepspeed_initialization():
    """测试DeepSpeed初始化"""
    try:
        # 创建一个简单的模型用于测试
        import torch.nn as nn
        
        class SimpleModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = nn.Linear(768, 10)
            
            def forward(self, x):
                return self.linear(x)
        
        model = SimpleModel()
        
        # 创建参数组
        optimizer_grouped_parameters = [
            {'params': model.parameters(), 'lr': 5e-5}
        ]
        
        # 加载配置
        with open("deepspeed_zero3_config.json", 'r') as f:
            ds_config = json.load(f)
        
        # 创建简单的args对象
        class Args:
            local_rank = -1

        args = Args()

        # 设置不同的端口避免冲突
        os.environ['MASTER_PORT'] = '29501'
        
        print("🔄 测试DeepSpeed初始化...")
        
        # 初始化DeepSpeed
        model_engine, optimizer, _, scheduler = deepspeed.initialize(
            args=args,
            model=model,
            model_parameters=optimizer_grouped_parameters,
            config=ds_config
        )
        
        print("✅ DeepSpeed初始化成功")
        print(f"   - 模型引擎类型: {type(model_engine)}")
        print(f"   - 优化器类型: {type(optimizer)}")
        print(f"   - 调度器类型: {type(scheduler)}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSpeed初始化失败: {e}")
        return False

def main():
    print("🧪 DeepSpeed ZeRO-3 集成测试")
    print("=" * 50)
    
    # 测试GPU可用性
    if torch.cuda.is_available():
        print(f"✅ CUDA可用，GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   - GPU {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("❌ CUDA不可用")
        return
    
    print("-" * 50)
    
    # 测试配置文件
    if not test_deepspeed_config():
        return
    
    print("-" * 50)
    
    # 测试模型加载
    if not test_model_loading():
        return
    
    print("-" * 50)
    
    # 测试DeepSpeed初始化
    if not test_deepspeed_initialization():
        return
    
    print("-" * 50)
    print("🎉 所有测试通过！DeepSpeed ZeRO-3 配置正确")
    print("\n📋 下一步:")
    print("1. 使用 'bash run_deepspeed_single.sh' 启动单GPU训练")
    print("2. 使用 'bash run_deepspeed.sh' 启动多GPU训练")
    print("3. 查看 'DEEPSPEED_README.md' 了解更多信息")

if __name__ == "__main__":
    main()
