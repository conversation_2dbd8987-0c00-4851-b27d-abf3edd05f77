#!/bin/bash

# DeepSpeed ZeRO-3 训练启动脚本
# 使用方法: bash run_deepspeed.sh

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3  # 根据您的GPU数量调整
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# DeepSpeed配置文件路径
DEEPSPEED_CONFIG="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/deepspeed_zero3_config.json"

# 训练脚本路径
TRAIN_SCRIPT="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL/train_tb.py"

# 模型路径
MODEL_PATH="/home/<USER>/ZhouSQ/DCX/Qwen3-8B"

# 结果文件路径
RESULT_FILE="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/few_shot_train.txt"

echo "🚀 启动DeepSpeed ZeRO-3训练..."
echo "配置文件: $DEEPSPEED_CONFIG"
echo "训练脚本: $TRAIN_SCRIPT"
echo "模型路径: $MODEL_PATH"

# 使用deepspeed命令启动训练
deepspeed --num_gpus=4 \
    --master_port=29500 \
    $TRAIN_SCRIPT \
    --model qwen \
    --model_name_or_path $MODEL_PATH \
    --result_file $RESULT_FILE \

echo "✅ 训练完成！"
