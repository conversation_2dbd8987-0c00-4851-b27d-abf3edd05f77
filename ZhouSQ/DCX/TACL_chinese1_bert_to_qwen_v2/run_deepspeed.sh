#!/bin/bash

# DeepSpeed ZeRO-3 训练启动脚本
# 使用方法: bash run_deepspeed.sh

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3  # 根据您的GPU数量调整
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# DeepSpeed配置文件路径
DEEPSPEED_CONFIG="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/deepspeed_zero3_config.json"

# 训练脚本路径
TRAIN_SCRIPT="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL/train_tb.py"

# 模型路径
MODEL_PATH="/home/<USER>/ZhouSQ/DCX/Qwen3-8B"

# 结果文件路径
RESULT_FILE="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/few_shot_train.txt"

echo "🚀 启动DeepSpeed ZeRO-3训练..."
echo "配置文件: $DEEPSPEED_CONFIG"
echo "训练脚本: $TRAIN_SCRIPT"
echo "模型路径: $MODEL_PATH"

# 使用deepspeed命令启动训练
deepspeed --num_gpus=4 \
    --master_port=29500 \
    --deepspeed_config $DEEPSPEED_CONFIG \
    $TRAIN_SCRIPT \
    --model qwen \
    --model_name_or_path $MODEL_PATH \
    --result_file $RESULT_FILE \
    --multi_label 0 \
    --multi_verb 1 \
    --constraint_loss 1 \
    --constraint_alpha 0.1 \
    --lm_training 1 \
    --lm_alpha 0.7 \
    --lr 3e-5 \
    --lr2 5e-4 \
    --contrastive_loss 1 \
    --contrastive_alpha 0.2 \
    --contrastive_level 2 \
    --batch_size 8 \
    --depth 7 \
    --multi_mask 1 \
    --dropout 0.1 \
    --shuffle 1 \
    --contrastive_logits 1 \
    --cs_mode 0 \
    --dataset wos \
    --eval_mode 0 \
    --use_hier_mean 1 \
    --freeze_plm 0 \
    --use_scheduler1 1 \
    --use_scheduler2 1 \
    --imbalanced_weight True \
    --imbalanced_weight_reverse True \
    --device -1 \
    --max_grad_norm 1.0 \
    --max_seq_lens 512 \
    --use_new_ct 1 \
    --use_dropout_sim 1 \
    --use_withoutWrappedLM False \
    --mean_verbalizer True \
    --shot 30 \
    --label_description 0 \
    --seed 171 \
    --plm_eval_mode True \
    --verbalizer soft \
    --template_id 0 \
    --not_manual False \
    --gradient_accumulation_steps 4 \
    --max_epochs 20 \
    --early_stop 5 \
    --eval_full 0 \
    --use_deepspeed 1 \
    --use_lora 0 \
    --lora_rank 32 \
    --lora_alpha 64 \
    --lora_dropout 0.05 \
    --lora_target_modules "q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj" \

echo "✅ 训练完成！"
