#!/usr/bin/env python3
"""
测试DeepSpeed修复是否正确
"""

import os
import sys
import argparse
from pathlib import Path

# 添加DCL目录到Python路径
sys.path.append(str(Path(__file__).parent / "DCL"))

def test_deepspeed_args():
    """测试DeepSpeed参数解析"""
    
    # 模拟命令行参数
    test_args = [
        "--model", "qwen",
        "--model_name_or_path", "/home/<USER>/ZhouSQ/DCX/Qwen3-8B",
        "--use_deepspeed", "1",
        "--deepspeed_config", "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/deepspeed_zero3_config.json",
        "--batch_size", "8",
        "--lr", "5e-5",
        "--max_epochs", "1",  # 只测试1个epoch
        "--dataset", "wos",
        "--shot", "30",
        "--seed", "171"
    ]
    
    # 导入训练脚本的参数解析器
    try:
        from train_tb import create_parser
        parser = create_parser()
        args = parser.parse_args(test_args)
        
        print("✅ 参数解析成功")
        print(f"   - use_deepspeed: {args.use_deepspeed}")
        print(f"   - deepspeed_config: {args.deepspeed_config}")
        print(f"   - model: {args.model}")
        print(f"   - batch_size: {args.batch_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数解析失败: {e}")
        return False

def test_config_loading():
    """测试配置文件加载"""
    
    config_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/deepspeed_zero3_config.json"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print("✅ 配置文件加载成功")
        print(f"   - ZeRO Stage: {config['zero_optimization']['stage']}")
        print(f"   - 微批次大小: {config['train_micro_batch_size_per_gpu']}")
        print(f"   - FP16: {config.get('fp16', {}).get('enabled', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def main():
    print("🔧 测试DeepSpeed修复")
    print("=" * 40)
    
    # 测试参数解析
    if not test_deepspeed_args():
        return
    
    print("-" * 40)
    
    # 测试配置文件
    if not test_config_loading():
        return
    
    print("-" * 40)
    print("🎉 所有测试通过！")
    print("\n📋 现在可以使用以下命令启动训练:")
    print("1. bash run_deepspeed_single.sh  # 单GPU")
    print("2. bash run_deepspeed.sh         # 多GPU")

if __name__ == "__main__":
    main()
