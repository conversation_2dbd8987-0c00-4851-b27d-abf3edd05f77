# DeepSpeed 配置冲突修复

## 🐛 问题描述

遇到了以下错误：
```
AssertionError: Not sure how to proceed, we were given deepspeed configs in the deepspeed arguments and deepspeed.initialize() function call
```

## 🔍 问题原因

这个错误是因为在使用 `deepspeed` 命令启动训练时，DeepSpeed配置被同时通过两种方式传递：

1. **命令行参数**: `deepspeed --deepspeed_config config.json`
2. **代码中传递**: `deepspeed.initialize(config=ds_config)`

DeepSpeed不允许同时使用这两种方式传递配置，因为会产生冲突。

## ✅ 修复方案

### 1. 修改训练脚本 (`DCL/train_tb.py`)

**修改前:**
```python
# 加载配置文件
import json
with open(args.deepspeed_config, 'r') as f:
    ds_config = json.load(f)

# 传递配置到initialize
prompt_model, optimizer1, _, scheduler1 = deepspeed.initialize(
    args=args,
    model=prompt_model,
    model_parameters=optimizer_grouped_parameters1,
    config=ds_config  # ❌ 这里会冲突
)
```

**修改后:**
```python
# 不需要手动加载配置文件，DeepSpeed会自动从命令行读取
prompt_model, optimizer1, _, scheduler1 = deepspeed.initialize(
    args=args,
    model=prompt_model,
    model_parameters=optimizer_grouped_parameters1
    # ✅ 不传递config参数
)
```

### 2. 更新启动脚本

确保启动脚本正确传递DeepSpeed配置：

```bash
deepspeed --num_gpus=1 \
    --master_port=29500 \
    --deepspeed_config $DEEPSPEED_CONFIG \  # ✅ 通过命令行传递配置
    $TRAIN_SCRIPT \
    --use_deepspeed 1 \
    --deepspeed_config $DEEPSPEED_CONFIG \  # ✅ 也传递给训练脚本（用于其他用途）
    # ... 其他参数
```

## 🧪 测试修复

### 快速测试
```bash
# 运行快速测试（1个epoch）
bash run_deepspeed_test.sh
```

### 完整训练
```bash
# 单GPU训练
bash run_deepspeed_single.sh

# 多GPU训练
bash run_deepspeed.sh
```

## 📋 修复内容总结

1. **移除了代码中的配置文件加载和传递**
   - 不再在 `deepspeed.initialize()` 中传递 `config` 参数
   - DeepSpeed会自动从命令行参数中读取配置

2. **更新了启动脚本**
   - 确保通过 `--deepspeed_config` 正确传递配置文件路径
   - 添加了所有必要的训练参数

3. **创建了测试脚本**
   - `run_deepspeed_test.sh`: 快速测试脚本（1个epoch）
   - `test_deepspeed_fix.py`: 参数解析测试脚本

## 🎯 使用建议

1. **首次使用**: 先运行 `bash run_deepspeed_test.sh` 进行快速测试
2. **确认无误**: 再使用完整的训练脚本
3. **监控训练**: 使用 `nvidia-smi` 监控GPU使用情况

## 📞 如果仍有问题

如果仍然遇到问题，请检查：

1. **DeepSpeed版本**: 确保使用兼容版本
2. **配置文件**: 确保JSON格式正确
3. **路径设置**: 确保所有路径都正确
4. **端口冲突**: 如果端口被占用，修改 `--master_port` 参数

---

🎉 **修复完成！现在可以正常使用DeepSpeed ZeRO-3进行训练了！**
