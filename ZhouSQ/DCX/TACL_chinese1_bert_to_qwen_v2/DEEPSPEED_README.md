# DeepSpeed ZeRO-3 训练指南

本项目已经适配了DeepSpeed ZeRO-3，可以大幅减少显存使用，支持训练更大的模型。

## 🚀 快速开始

### 1. 环境要求

确保已安装以下依赖：
```bash
pip install deepspeed
pip install transformers
pip install torch
```

### 2. 启动训练

#### 单GPU训练
```bash
bash run_deepspeed_single.sh
```

#### 多GPU训练
```bash
bash run_deepspeed.sh
```

#### 手动启动（推荐用于调试）
```bash
python DCL/train_tb.py --use_deepspeed 1 --deepspeed_config deepspeed_zero3_config.json
```

## ⚙️ 配置说明

### DeepSpeed配置文件 (`deepspeed_zero3_config.json`)

主要配置项说明：

- **ZeRO Stage 3**: 参数分片，最大化显存节省
- **CPU Offload**: 将优化器状态和参数offload到CPU，进一步节省显存
- **FP16**: 使用半精度浮点数，减少显存使用
- **Gradient Accumulation**: 梯度累积，模拟更大的batch size

### 关键参数

```json
{
  "train_micro_batch_size_per_gpu": 2,    // 每个GPU的微批次大小
  "gradient_accumulation_steps": 4,       // 梯度累积步数
  "zero_optimization": {
    "stage": 3,                           // ZeRO Stage 3
    "offload_optimizer": {
      "device": "cpu",                    // 优化器状态offload到CPU
      "pin_memory": true
    },
    "offload_param": {
      "device": "cpu",                    // 参数offload到CPU
      "pin_memory": true
    }
  },
  "fp16": {
    "enabled": true                       // 启用FP16
  }
}
```

## 🔧 训练参数

### 主要训练参数

- `--use_deepspeed 1`: 启用DeepSpeed
- `--deepspeed_config`: DeepSpeed配置文件路径
- `--batch_size`: 全局批次大小
- `--gradient_accumulation_steps`: 梯度累积步数
- `--lr`: 学习率
- `--max_epochs`: 最大训练轮数

### 显存优化参数

- `--use_lora 1`: 启用LoRA微调（进一步减少显存）
- `--freeze_plm 1`: 冻结预训练模型参数

## 📊 性能对比

| 模式 | 显存使用 | 训练速度 | 模型大小支持 |
|------|----------|----------|--------------|
| 标准训练 | ~24GB | 快 | 小模型 |
| DeepSpeed ZeRO-3 | ~8GB | 中等 | 大模型 |
| DeepSpeed + LoRA | ~6GB | 中等 | 超大模型 |

## 🐛 常见问题

### 1. 显存不足
- 减少 `train_micro_batch_size_per_gpu`
- 增加 `gradient_accumulation_steps`
- 启用CPU offload
- 使用LoRA微调

### 2. 训练速度慢
- 检查CPU offload设置
- 调整 `stage3_prefetch_bucket_size`
- 使用更快的存储设备

### 3. 模型保存/加载问题
- DeepSpeed模式下使用 `save_checkpoint()` 和 `load_checkpoint()`
- 确保所有进程都能访问保存路径

## 📝 注意事项

1. **多GPU训练**: 确保所有GPU可见且可用
2. **内存管理**: CPU offload需要足够的系统内存
3. **模型保存**: DeepSpeed模式下的模型保存格式与标准PyTorch不同
4. **调试**: 建议先用单GPU测试，确认无误后再使用多GPU

## 🔍 监控训练

训练过程中可以通过以下方式监控：

1. **TensorBoard**: 查看loss曲线和指标
2. **显存监控**: `nvidia-smi` 查看显存使用
3. **日志文件**: 查看详细的训练日志

## 📞 技术支持

如果遇到问题，请检查：
1. DeepSpeed版本兼容性
2. CUDA版本兼容性
3. 配置文件格式正确性
4. 路径设置正确性
